import 'package:serwis_app/core/models/task.dart';
import 'package:serwis_app/core/repository.dart';
import 'package:serwis_app/core/utils/value_wrapper.dart';

class TaskRepository extends AuthenticatedRepository {
  TaskRepository();

  Future<void> updateTaskStatus(Task task, TaskStatus newStatus, String? newComment) async {
    await api.put(
      '/api/task/${task.id}/update',
      data: task
          .copyWith(
            status: newStatus,
            servicemanSolution: Value(newComment),
          )
          .toMap(),
    );
  }
}

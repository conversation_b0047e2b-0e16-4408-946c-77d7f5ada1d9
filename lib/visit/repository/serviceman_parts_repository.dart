import 'package:serwis_app/core/repository.dart';
import 'package:serwis_app/visit/models/serviceman_part.dart';

class ServicemanPartsRepository extends AuthenticatedRepository {
  ServicemanPartsRepository();

  Future<List<ServicemanPart>> getParts() async {
    final response = await api.get('/api/parts');
    final data = response.data['data'] as List<dynamic>;
    final parts = data.map((e) => ServicemanPart.fromMap(e)).toList();

    return parts;
  }
}

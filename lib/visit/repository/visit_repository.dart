import 'package:dio/dio.dart';

import 'package:serwis_app/core/models/form_field_schema.dart';
import 'package:serwis_app/core/models/visit/visit.dart';
import 'package:serwis_app/core/models/visit/visit_form_type.dart';
import 'package:serwis_app/core/models/visit/visit_list.dart';
import 'package:serwis_app/core/repository.dart';

class VisitRepository extends AuthenticatedRepository {
  VisitRepository();

  Future<VisitList> getVisits({List<VisitModelStatus>? statusList}) async {
    final response = await api.get('/api/visits', queryParameters: {
      if (statusList != null) 'status': statusList.join(','),
    });
    final data = VisitList.fromMap(response.data);

    return data;
  }

  Future<Visit> getVisit(int visitId) async {
    final response = await api.get('/api/visit/$visitId');
    final data = Visit.fromMap(response.data);

    return data;
  }

  Future<VisitModelStatus> arriveAtVisit(int visitId) async {
    final response = await api.post(
      '/api/visit/$visitId/status/attended',
      data: {
        'comment': null,
      },
    );
    final data = VisitModelStatus.fromString(response.data['status']);

    return data;
  }

  Future<List<FormFieldSchema>> getVisitForms(int visitId, VisitFormType formType) async {
    final response = await api.get('/api/visit/$visitId/forms/${formType.value}',
        options: Options(
          headers: {'Accept': 'application/json'},
        ));
    final data = response.data['schema'] as List<dynamic>;
    final fields = data.map((e) => FormFieldSchema.fromJson(e)).toList();

    return fields;
  }

  Future<void> submitVisitForm(int visitId, VisitFormType formType, Map<String, dynamic> formData) async {
    await api.post('/api/visit/$visitId/forms/${formType.value}', data: formData);
  }
}

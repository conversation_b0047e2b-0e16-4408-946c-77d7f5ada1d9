import 'dart:convert';
import 'dart:io';

import 'package:serwis_app/core/models/visit/visit_photo_list.dart';
import 'package:serwis_app/core/repository.dart';
import 'package:serwis_app/visit/services/visit_photos_cache_service.dart';

class VisitPhotosRepository extends AuthenticatedRepository {
  final VisitPhotosCacheService _cacheService;

  VisitPhotosRepository({
    required VisitPhotosCacheService cacheService,
  }) : _cacheService = cacheService;

  Future<VisitPhotoList> getVisitPhotos(int visitId) async {
    // Najpierw sprawdź cache
    final cachedPhotos = await _cacheService.getCachedPhotos(visitId);
    if (cachedPhotos != null) {
      return VisitPhotoList(data: cachedPhotos, count: cachedPhotos.length);
    }

    // Cache miss - pobierz z API
    final response = await api.get('/api/visit/$visitId/photos');
    final data = VisitPhotoList.fromMap(response.data);

    // Zapisz do cache'u
    await _cacheService.setCachedPhotos(visitId, data.data);

    return data;
  }

  Future<void> addVisitPhoto(int visitId, File photoFile) async {
    await api.post('/api/visit/$visitId/photos', data: {
      'fileContent': base64Encode(await photoFile.readAsBytes()),
      'category': 'OTHR',
      'subCategory': null,
    });

    // Wyczyść cache po dodaniu nowego zdjęcia
    await _cacheService.clearVisitCache(visitId);
  }

  Future<void> deleteVisitPhoto(int photoId, int visitId) async {
    await api.delete('/api/photo/$photoId');

    // Wyczyść cache po usunięciu zdjęcia
    await _cacheService.clearVisitCache(visitId);
  }
}

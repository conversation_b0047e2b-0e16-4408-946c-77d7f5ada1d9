import 'package:serwis_app/core/repository.dart';
import 'package:serwis_app/visit/models/serviceman_part.dart';
import 'package:serwis_app/visit/models/visit_part.dart';

class VisitPartsRepository extends AuthenticatedRepository {
  VisitPartsRepository();

  Future<List<VisitPart>> getVisitParts(int visitId) async {
    final response = await api.get('/api/visit/$visitId/parts');
    final data = response.data['data'] as List<dynamic>;
    final parts = data.map((e) => VisitPart.fromMap(e)).toList();

    return parts;
  }

  Future<void> addVisitParts(int visitId, List<UsedPart> usedParts) async {
    await api.post(
      '/api/visit/$visitId/parts/multi',
      data: {
        'data': usedParts.map((e) => e.toMap()).toList(),
      },
    );
  }
}

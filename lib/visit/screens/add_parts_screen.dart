import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:serwis_app/visit/cubit/visit_parts_cubit.dart';
import 'package:serwis_app/visit/widgets/exit_parts_confirmation_modal.dart';
import 'package:serwis_app/visit/widgets/parts/parts_search_field.dart';
import 'package:serwis_app/visit/widgets/parts/parts_search_results.dart';
import 'package:serwis_app/visit/widgets/parts/selected_parts_list.dart';

/// Ekran dodawania części do wizyty
class AddPartsScreen extends StatelessWidget {
  const AddPartsScreen({super.key});

  /// Określa czy pokazać wybrane części czy wyniki wyszukiwania
  bool _shouldShowSelectedParts(VisitPartsState state) {
    return state.selectedParts.isNotEmpty &&
        state.searchQuery.isEmpty &&
        state.status != VisitPartsStatus.loadingAvailableParts;
  }

  @override
  Widget build(BuildContext context) {
    final visit = context.read<VisitPartsCubit>().visit;

    return Scaffold(
      appBar: _buildAppBar(context, visit),
      body: BlocListener<VisitPartsCubit, VisitPartsState>(
        listener: (context, state) {
          if (state.status == VisitPartsStatus.partsSubmitted) {
            Navigator.of(context).pop();
          }
        },
        child: BlocBuilder<VisitPartsCubit, VisitPartsState>(
          builder: (context, state) {
            return PopScope(
              canPop: state.selectedParts.isEmpty,
              onPopInvokedWithResult: (didPop, result) async {
                if (!didPop) {
                  final shouldExit = await ExitPartsConfirmationModal.show(context);
                  if (shouldExit == true && context.mounted) {
                    Navigator.of(context).pop();
                    context.read<VisitPartsCubit>().onCloseWithoutSaving();
                  }
                }
              },
              child: _buildBody(context, state),
            );
          },
        ),
      ),
    );
  }

  /// Buduje AppBar z tytułem wizyty
  AppBar _buildAppBar(BuildContext context, dynamic visit) {
    return AppBar(
      title: ListTile(
        contentPadding: EdgeInsets.zero,
        title: Text(
          'Wizyta ${visit.incidentId}/${visit.visitNumber}',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        subtitle: const Text('Dodaj części'),
      ),
    );
  }

  /// Buduje główną zawartość ekranu
  Widget _buildBody(BuildContext context, VisitPartsState state) {
    final showSelectedParts = _shouldShowSelectedParts(state);

    return ScrollbarTheme(
      data: ScrollbarThemeData(
        thumbColor: WidgetStateProperty.all(Colors.white30),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Pole wyszukiwania
            const PartsSearchField(),
            const SizedBox(height: 16),

            // Zawartość główna - wyniki wyszukiwania lub wybrane części
            if (showSelectedParts)
              const Expanded(child: SelectedPartsList())
            else
              const Expanded(child: PartsSearchResults()),
          ],
        ),
      ),
    );
  }
}

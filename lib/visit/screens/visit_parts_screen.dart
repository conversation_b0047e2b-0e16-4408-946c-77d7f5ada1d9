import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:serwis_app/core/widgets/error_dialog.dart';
import 'package:serwis_app/visit/cubit/visit_parts_cubit.dart';
import 'package:serwis_app/visit/screens/add_parts_screen.dart';
import 'package:serwis_app/visit/views/visit_parts_list_view.dart';

class VisitPartsScreen extends StatelessWidget {
  const VisitPartsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final visit = context.read<VisitPartsCubit>().visit;

    return BlocListener<VisitPartsCubit, VisitPartsState>(
      listener: (context, state) {
        if (state.error != null) {
          ErrorDialog.show(context, state.error);
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: ListTile(
            contentPadding: EdgeInsets.zero,
            title: Text(
              'Wizyta ${visit.incidentId}/${visit.visitNumber}',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            subtitle: const Text('Uzupełnij c<PERSON>ś<PERSON>'),
          ),
        ),
        body: BlocSelector<VisitPartsCubit, VisitPartsState, VisitPartsStatus>(
          selector: (state) => state.status,
          builder: (context, status) {
            if (status == VisitPartsStatus.loadingVisitParts) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            }

            return const VisitPartsListView();
          },
        ),
        floatingActionButton: FloatingActionButton.extended(
          onPressed: () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (_) => BlocProvider.value(
                  value: context.read<VisitPartsCubit>()..searchParts(null),
                  child: const AddPartsScreen(),
                ),
              ),
            );
          },
          label: const Text('Dodaj część'),
          icon: const Icon(Icons.add),
        ),
      ),
    );
  }
}

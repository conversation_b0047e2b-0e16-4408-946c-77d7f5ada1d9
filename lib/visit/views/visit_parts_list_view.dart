import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:serwis_app/visit/cubit/visit_parts_cubit.dart';
import 'package:serwis_app/visit/models/visit_part.dart';

class VisitPartsListView extends StatelessWidget {
  const VisitPartsListView({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocSelector<VisitPartsCubit, VisitPartsState, List<VisitPart>>(
      selector: (state) => state.visitParts,
      builder: (context, visitParts) {
        if (visitParts.isEmpty) {
          return const Center(
            child: Text('Nie dodano <PERSON> c<PERSON>'),
          );
        }

        return ListView.separated(
          padding: const EdgeInsets.all(8.0),
          itemCount: visitParts.length,
          separatorBuilder: (context, index) => const SizedBox(height: 8),
          itemBuilder: (context, index) {
            // final part = visitParts[index];
            return ListTile(
              title: Text('Część ${index + 1}'),
              // title: Text(part.itemDescription),
              // subtitle: Text('${part.part.itemCode}'),
              // trailing: Text('${part.quantity}'),
            );
          },
        );
      },
    );
  }
}

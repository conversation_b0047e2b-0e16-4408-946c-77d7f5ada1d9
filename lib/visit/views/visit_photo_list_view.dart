import 'dart:io';

import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:serwis_app/core/utils/context_extensions.dart';
import 'package:serwis_app/core/widgets/error_container.dart';
import 'package:serwis_app/core/widgets/info_container.dart';
import 'package:serwis_app/visit/cubit/visit_photos_cubit.dart';
import 'package:serwis_app/visit/widgets/delete_photo_confirmation_modal.dart';

class VisitPhotoListView extends StatelessWidget {
  const VisitPhotoListView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<VisitPhotosCubit, VisitPhotosState, VisitPhotosState>(
      selector: (state) => state,
      builder: (context, state) {
        final photos = state.photos;
        final failedPhotos = state.failedPhotos;
        final isUploadingPhoto = state.status == VisitPhotosStatus.uploadingPhoto;
        final totalItems = photos.length + failedPhotos.length + (isUploadingPhoto ? 1 : 0);

        Widget widget = const Center(
          child: Text('<PERSON>rak zd<PERSON>'),
        );

        if (totalItems > 0) {
          widget = Column(
            children: [
              if (failedPhotos.isNotEmpty)
                ErrorContainer(
                  error: 'Nie udało się wczytać ${failedPhotos.length} zdjęć',
                  message: 'Niektóre pliki mogą być uszkodzone',
                  visible: true,
                ),
              if (photos.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: const InfoContainer(
                    message: 'Przytrzymaj na zdjęciu, aby je usunąć',
                  ),
                ),
              Expanded(
                child: GridView.builder(
                  padding: const EdgeInsets.all(8.0),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 8.0,
                    mainAxisSpacing: 8.0,
                    childAspectRatio: 1.0,
                  ),
                  itemCount: totalItems,
                  itemBuilder: (context, index) {
                    if (index < photos.length) {
                      return _buildPhotoItem(context, photos[index]);
                    } else if (isUploadingPhoto && index == photos.length) {
                      return _buildUploadingPhotoPlaceholder(context);
                    } else {
                      final failedIndex = index - photos.length;
                      return _buildFailedPhotoPlaceholder(failedPhotos[failedIndex]);
                    }
                  },
                ),
              ),
            ],
          );
        }

        return PopScope(
          canPop: state.status != VisitPhotosStatus.uploadingPhoto,
          onPopInvokedWithResult: (didPop, result) {
            if (!didPop) {
              context.showSnackbar('Proszę poczekać na zakończenie przesyłania zdjęć');
            }
          },
          child: widget,
        );
      },
    );
  }

  Widget _buildPhotoItem(BuildContext context, File photo) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8.0),
      child: Material(
        child: Ink.image(
          image: FileImage(photo),
          fit: BoxFit.cover,
          child: InkWell(
            onLongPress: () => _showDeleteConfirmation(context, photo),
          ),
        ),
      ),
    );
  }

  Widget _buildUploadingPhotoPlaceholder(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHigh,
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, File photo) {
    DeletePhotoConfirmationModal.show(
      context,
      photoFile: photo,
      cubit: context.read<VisitPhotosCubit>(),
    );
  }

  Widget _buildFailedPhotoPlaceholder(String filename) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(color: Colors.grey[400]!),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.broken_image,
            size: 40,
            color: Colors.grey[600],
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Text(
              'Plik uszkodzony',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 4),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Text(
              filename,
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 10,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}

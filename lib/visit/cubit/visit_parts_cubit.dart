import 'package:hydrated_bloc/hydrated_bloc.dart';

import 'package:serwis_app/core/models/visit/visit.dart';
import 'package:serwis_app/visit/models/serviceman_part.dart';
import 'package:serwis_app/visit/models/visit_part.dart';
import 'package:serwis_app/visit/repository/serviceman_parts_repository.dart';
import 'package:serwis_app/visit/repository/visit_parts_repository.dart';

enum VisitPartsStatus {
  loadingVisitParts,
  loadingAvailableParts,
  submittingParts,
  partsSubmitted,
  ready,
}

class VisitPartsState {
  final VisitPartsStatus status;
  final List<VisitPart> visitParts;
  final List<ServicemanPart> filteredParts;
  final List<ServicemanPart> selectedParts;
  final Map<int, double> selectedPartsQuantities; // itemGID -> quantity
  final String searchQuery;
  final dynamic error;

  const VisitPartsState({
    required this.status,
    required this.visitParts,
    required this.filteredParts,
    required this.selectedParts,
    required this.selectedPartsQuantities,
    required this.searchQuery,
    this.error,
  });

  factory VisitPartsState.initial() {
    return const VisitPartsState(
      status: VisitPartsStatus.loadingVisitParts,
      visitParts: [],
      filteredParts: [],
      selectedParts: [],
      selectedPartsQuantities: {},
      searchQuery: '',
    );
  }

  VisitPartsState copyWith({
    VisitPartsStatus? status,
    List<VisitPart>? visitParts,
    List<ServicemanPart>? filteredParts,
    List<ServicemanPart>? selectedParts,
    Map<int, double>? selectedPartsQuantities,
    String? searchQuery,
    dynamic error,
  }) {
    return VisitPartsState(
      status: status ?? this.status,
      visitParts: visitParts ?? this.visitParts,
      filteredParts: filteredParts ?? this.filteredParts,
      selectedParts: selectedParts ?? this.selectedParts,
      selectedPartsQuantities: selectedPartsQuantities ?? this.selectedPartsQuantities,
      searchQuery: searchQuery ?? this.searchQuery,
      error: error,
    );
  }
}

class VisitPartsCubit extends Cubit<VisitPartsState> {
  final VisitPartsRepository visitPartsRepository;
  final ServicemanPartsRepository partsRepository;
  final Visit visit;

  VisitPartsCubit({
    required this.visitPartsRepository,
    required this.partsRepository,
    required this.visit,
  }) : super(VisitPartsState.initial());

  Future<void> getVisitParts() async {
    try {
      emit(state.copyWith(status: VisitPartsStatus.loadingVisitParts));

      final parts = await visitPartsRepository.getVisitParts(visit.id);

      emit(state.copyWith(status: VisitPartsStatus.ready, visitParts: parts));
    } catch (e) {
      emit(state.copyWith(status: VisitPartsStatus.ready, error: e));
    }
  }

  void searchParts(String? query) async {
    if (query == state.searchQuery) return;

    try {
      emit(state.copyWith(status: VisitPartsStatus.loadingAvailableParts));
      query ??= '';
      final normalizedQuery = query.toLowerCase().trim();

      if (state.selectedParts.isNotEmpty && normalizedQuery.isEmpty) {
        emit(state.copyWith(
          status: VisitPartsStatus.ready,
          searchQuery: query,
          filteredParts: [],
        ));
        return;
      }

      final availableParts = await partsRepository.getParts();

      final filtered = availableParts.where((part) {
        // Wykluczaj już wybrane części
        final isAlreadySelected = state.selectedParts.any((selected) => selected.itemGID == part.itemGID);
        if (isAlreadySelected) return false;

        return part.itemCode.toLowerCase().contains(normalizedQuery) ||
            part.itemDescription.toLowerCase().contains(normalizedQuery);
      }).toList();

      emit(state.copyWith(
        status: VisitPartsStatus.ready,
        searchQuery: query,
        filteredParts: filtered,
      ));

      getVisitParts();
    } catch (e) {
      emit(state.copyWith(status: VisitPartsStatus.ready, error: e));
    }
  }

  void selectPart(ServicemanPart part) {
    // Sprawdź czy część nie jest już wybrana
    if (state.selectedParts.any((p) => p.itemGID == part.itemGID)) {
      return;
    }

    final updatedSelectedParts = List<ServicemanPart>.from(state.selectedParts)..add(part);
    final updatedQuantities = Map<int, double>.from(state.selectedPartsQuantities);
    updatedQuantities[part.itemGID] = 1.0; // Domyślna ilość: 1

    emit(state.copyWith(
      selectedParts: updatedSelectedParts,
      selectedPartsQuantities: updatedQuantities,
      searchQuery: '',
      filteredParts: [],
    ));
  }

  void removePart(ServicemanPart part) {
    final updatedSelectedParts = state.selectedParts.where((p) => p.itemGID != part.itemGID).toList();
    final updatedQuantities = Map<int, double>.from(state.selectedPartsQuantities);
    updatedQuantities.remove(part.itemGID);

    emit(state.copyWith(
      selectedParts: updatedSelectedParts,
      selectedPartsQuantities: updatedQuantities,
    ));

    if (updatedSelectedParts.isEmpty) {
      searchParts(null);
    }
  }

  void submitParts() async {
    try {
      emit(state.copyWith(status: VisitPartsStatus.submittingParts));

      final usedParts = state.selectedParts.map((part) {
        final quantity = state.selectedPartsQuantities[part.itemGID] ?? 1.0;
        return UsedPart.fromPart(part, quantity);
      }).toList();

      await visitPartsRepository.addVisitParts(visit.id, usedParts);

      emit(state.copyWith(
        status: VisitPartsStatus.partsSubmitted,
        selectedParts: [],
        selectedPartsQuantities: {},
        searchQuery: '',
        filteredParts: [],
      ));
      getVisitParts();
    } catch (e) {
      emit(state.copyWith(status: VisitPartsStatus.ready, error: e));
    }
  }

  void updatePartQuantity(int itemGID, double quantity) {
    final updatedQuantities = Map<int, double>.from(state.selectedPartsQuantities);
    updatedQuantities[itemGID] = quantity;

    emit(state.copyWith(selectedPartsQuantities: updatedQuantities));
  }

  void clearSearch() {
    emit(state.copyWith(
      searchQuery: '',
      filteredParts: [],
    ));
  }

  void onCloseWithoutSaving() {
    emit(state.copyWith(
      status: VisitPartsStatus.ready,
      selectedParts: [],
      selectedPartsQuantities: {},
      searchQuery: '',
      filteredParts: [],
    ));
  }
}

import 'dart:convert';

import 'package:equatable/equatable.dart';

class ServicemanPart extends Equatable {
  final int itemGID;
  final String itemCode;
  final String itemDescription;
  final int warehouseQuantity;
  final String unit;
  final bool isOwnedByBKF;
  final String? symptoms;
  final String? possibleCause;
  final String? servicemanRemarks;
  final String? defaultScrapAction;
  final String? uniquePartTag;

  const ServicemanPart({
    required this.itemGID,
    required this.itemCode,
    required this.itemDescription,
    required this.warehouseQuantity,
    required this.unit,
    required this.isOwnedByBKF,
    required this.symptoms,
    required this.possibleCause,
    required this.servicemanRemarks,
    required this.defaultScrapAction,
    required this.uniquePartTag,
  });

  @override
  List<Object?> get props => [
        itemGID,
        itemCode,
        itemDescription,
        warehouseQuantity,
        unit,
        isOwnedByBKF,
        symptoms,
        possibleCause,
        servicemanRemarks,
        defaultScrapAction,
        uniquePartTag,
      ];

  factory ServicemanPart.fromMap(Map<String, dynamic> map) {
    return ServicemanPart(
      itemGID: map['itemGID']?.toInt() ?? 0,
      itemCode: map['itemCode'] ?? '',
      itemDescription: map['itemDescription'] ?? '',
      warehouseQuantity: map['warehouseQuantity']?.toInt() ?? 0,
      unit: map['unit'] ?? '',
      isOwnedByBKF: map['isOwnedByBKF'] ?? false,
      symptoms: map['symptoms'],
      possibleCause: map['possibleCause'],
      servicemanRemarks: map['servicemanRemarks'],
      defaultScrapAction: map['defaultScrapAction'],
      uniquePartTag: map['uniquePartTag'],
    );
  }

  factory ServicemanPart.fromJson(String source) => ServicemanPart.fromMap(json.decode(source));

  Map<String, dynamic> toMap() {
    return {
      'itemGID': itemGID,
      'itemCode': itemCode,
      'itemDescription': itemDescription,
      'warehouseQuantity': warehouseQuantity,
      'unit': unit,
      'isOwnedByBKF': isOwnedByBKF,
      'symptoms': symptoms,
      'possibleCause': possibleCause,
      'servicemanRemarks': servicemanRemarks,
      'defaultScrapAction': defaultScrapAction,
      'uniquePartTag': uniquePartTag,
    };
  }

  String toJson() => json.encode(toMap());
}

class UsedPart {
  final int itemGID;
  final String itemCode;
  final double quantityUsed;
  final String unit;

  const UsedPart({
    required this.itemGID,
    required this.itemCode,
    required this.quantityUsed,
    required this.unit,
  });

  Map<String, dynamic> toMap() {
    return {
      'itemGID': itemGID,
      'itemCode': itemCode,
      'quantityUsed': quantityUsed,
      'unit': unit,
    };
  }

  String toJson() => json.encode(toMap());

  factory UsedPart.fromPart(ServicemanPart part, double quantity) {
    return UsedPart(
      itemGID: part.itemGID,
      itemCode: part.itemCode,
      quantityUsed: quantity,
      unit: part.unit,
    );
  }
}

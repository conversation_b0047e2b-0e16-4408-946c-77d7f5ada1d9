import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:serwis_app/core/models/visit/visit.dart';
import 'package:serwis_app/core/services/photo_service.dart';
import 'package:serwis_app/core/widgets/loading_widget.dart';
import 'package:serwis_app/visit/cubit/visit_cubit.dart';
import 'package:serwis_app/visit/cubit/visit_parts_cubit.dart';
import 'package:serwis_app/visit/cubit/visit_photos_cubit.dart';
import 'package:serwis_app/visit/repository/serviceman_parts_repository.dart';
import 'package:serwis_app/visit/repository/visit_parts_repository.dart';
import 'package:serwis_app/visit/repository/visit_photos_repository.dart';
import 'package:serwis_app/visit/screens/visit_parts_screen.dart';
import 'package:serwis_app/visit/screens/visit_photos_screen.dart';

class VisitActionButtons extends StatelessWidget {
  const VisitActionButtons({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerLowest,
        boxShadow: [
          BoxShadow(
            color: Colors.black54,
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: BlocSelector<VisitCubit, VisitState, VisitModelStatus>(
        selector: (state) => state.visit.status,
        builder: (context, visitStatus) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (visitStatus == VisitModelStatus.attended)
                // Pierwszy wiersz - dwa przyciski (mniejsze, mniej widoczne)
                Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      FilledButton.icon(
                        onPressed: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (_) {
                                return BlocProvider(
                                  create: (_) {
                                    return VisitPhotosCubit(
                                      photoService: context.read<PhotoService>(),
                                      visitPhotosRepository: context.read<VisitPhotosRepository>(),
                                      visit: context.read<VisitCubit>().state.visit,
                                    )..loadPhotos();
                                  },
                                  child: VisitPhotosScreen(),
                                );
                              },
                            ),
                          );
                        },
                        icon: const Icon(Icons.add_a_photo, size: 18),
                        label: const Text(
                          'Dodaj zdjęcia',
                          style: TextStyle(fontSize: 12),
                        ),
                        style: FilledButton.styleFrom(
                          backgroundColor: Theme.of(context).colorScheme.secondaryContainer,
                          foregroundColor: Theme.of(context).colorScheme.onSecondaryContainer,
                        ),
                      ),
                      FilledButton.icon(
                        onPressed: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (_) {
                                return BlocProvider(
                                  create: (_) {
                                    return VisitPartsCubit(
                                      visitPartsRepository: context.read<VisitPartsRepository>(),
                                      partsRepository: context.read<ServicemanPartsRepository>(),
                                      visit: context.read<VisitCubit>().state.visit,
                                    )..getVisitParts();
                                  },
                                  child: VisitPartsScreen(),
                                );
                              },
                            ),
                          );
                        },
                        icon: const Icon(Icons.home_repair_service, size: 18),
                        label: const Text(
                          'Uzupełnij części',
                          style: TextStyle(fontSize: 12),
                        ),
                        style: FilledButton.styleFrom(
                          backgroundColor: Theme.of(context).colorScheme.secondaryContainer,
                          foregroundColor: Theme.of(context).colorScheme.onSecondaryContainer,
                        ),
                      ),
                    ],
                  ),
                ),
              // Drugi wiersz - jeden przycisk (najważniejszy, bardziej wyróżniony)
              BlocSelector<VisitCubit, VisitState, VisitStatus>(
                selector: (state) => state.status,
                builder: (context, status) {
                  return SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: status != VisitStatus.loading
                          ? () {
                              context.read<VisitCubit>().onMainButtonPressed();
                            }
                          : null,
                      iconAlignment: visitStatus == VisitModelStatus.attended ? IconAlignment.end : IconAlignment.start,
                      icon: _buildMainButtonIcon(status, visitStatus),
                      label: LoadingWidget(
                        isLoading: status == VisitStatus.loading,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              visitStatus == VisitModelStatus.planned ? 'Jestem na miejscu' : 'Chcę zakończyć wizytę',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            if (visitStatus == VisitModelStatus.planned)
                              const Text(
                                'Potwierdź przybycie i rozpocznij wizytę',
                                style: TextStyle(fontSize: 12),
                              )
                          ],
                        ),
                      ),
                      // style: ElevatedButton.styleFrom(
                      //   backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                      //   foregroundColor: Theme.of(context).colorScheme.onPrimaryContainer,
                      //   padding: const EdgeInsets.symmetric(vertical: 16.0),
                      //   elevation: 4,
                      // ),
                    ),
                  );
                },
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildMainButtonIcon(VisitStatus status, VisitModelStatus visitStatus) {
    if (status == VisitStatus.loading) {
      return const SizedBox.shrink();
    }

    if (visitStatus == VisitModelStatus.planned) {
      return const Icon(Icons.check_circle, size: 24);
    }

    if (visitStatus == VisitModelStatus.attended) {
      return const Icon(Icons.chevron_right, size: 24);
    }

    return const SizedBox.shrink();
  }
}

import 'package:flutter/material.dart';

/// Komponent wyświetlający informacje o części w kompaktowym formacie
class PartInfoDisplay extends StatelessWidget {
  final String itemCode;
  final String itemDescription;
  final int warehouseQuantity;
  final String unit;
  final bool showWarehouse;

  const PartInfoDisplay({
    super.key,
    required this.itemCode,
    required this.itemDescription,
    required this.warehouseQuantity,
    required this.unit,
    this.showWarehouse = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Kod części w kontenerze
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(6),
          ),
          child: Text(
            itemCode,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w700,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
          ),
        ),
        const SizedBox(height: 8),

        // Opis części
        Text(
          itemDescription,
          style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),

        // Informacja o magazynie (opcjonalna)
        if (showWarehouse) ...[
          const SizedBox(height: 8),
          _WarehouseInfo(
            warehouseQuantity: warehouseQuantity,
            unit: unit,
          ),
        ],
      ],
    );
  }
}

/// Komponent wyświetlający informacje o dostępności w magazynie
class _WarehouseInfo extends StatelessWidget {
  final int warehouseQuantity;
  final String unit;

  const _WarehouseInfo({
    required this.warehouseQuantity,
    required this.unit,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'W magazynie: ',
            style: Theme.of(context).textTheme.bodySmall,
          ),
          Text(
            '$warehouseQuantity $unit',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
        ],
      ),
    );
  }
}

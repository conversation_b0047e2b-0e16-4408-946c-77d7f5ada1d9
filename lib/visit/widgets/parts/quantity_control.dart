import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:serwis_app/visit/cubit/visit_parts_cubit.dart';

/// Kompaktowa kontrolka do zmiany ilości części
class QuantityControl extends StatelessWidget {
  final int itemGID;
  final double quantity;
  final String unit;

  const QuantityControl({
    super.key,
    required this.itemGID,
    required this.quantity,
    required this.unit,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Kompaktowe kontrolki ilości
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Przycisk minus
              _QuantityButton(
                icon: Icons.remove,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  bottomLeft: Radius.circular(8),
                ),
                onTap: quantity > 1
                    ? () {
                        context.read<VisitPartsCubit>().updatePartQuantity(
                              itemGID,
                              quantity - 1,
                            );
                      }
                    : null,
                isEnabled: quantity > 1,
              ),

              // Pole ilości
              SizedBox(
                width: 55,
                height: 36,
                child: Center(
                  child: TextFormField(
                    key: ValueKey('quantity_${itemGID}_$quantity'),
                    initialValue: quantity % 1 == 0 ? quantity.toInt().toString() : quantity.toString(),
                    textAlign: TextAlign.center,
                    keyboardType: TextInputType.number,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.zero,
                      isDense: true,
                    ),
                    onChanged: (value) {
                      final newQuantity = double.tryParse(value);
                      if (newQuantity != null && newQuantity > 0) {
                        context.read<VisitPartsCubit>().updatePartQuantity(
                              itemGID,
                              newQuantity,
                            );
                      }
                    },
                  ),
                ),
              ),

              // Przycisk plus
              _QuantityButton(
                icon: Icons.add,
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(8),
                  bottomRight: Radius.circular(8),
                ),
                onTap: () {
                  context.read<VisitPartsCubit>().updatePartQuantity(
                        itemGID,
                        quantity + 1,
                      );
                },
                isEnabled: true,
              ),
            ],
          ),
        ),
        const SizedBox(width: 8),
        Text(
          unit,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
      ],
    );
  }
}

/// Przycisk kontrolki ilości
class _QuantityButton extends StatelessWidget {
  final IconData icon;
  final BorderRadius borderRadius;
  final VoidCallback? onTap;
  final bool isEnabled;

  const _QuantityButton({
    required this.icon,
    required this.borderRadius,
    required this.onTap,
    required this.isEnabled,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: borderRadius,
        onTap: onTap,
        child: Container(
          width: 36,
          height: 36,
          alignment: Alignment.center,
          child: Icon(
            icon,
            size: 16,
            color: isEnabled ? null : Colors.grey,
          ),
        ),
      ),
    );
  }
}

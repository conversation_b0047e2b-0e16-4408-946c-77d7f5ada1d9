import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:serwis_app/visit/cubit/visit_parts_cubit.dart';
import 'package:serwis_app/visit/widgets/parts/part_info_display.dart';
import 'package:serwis_app/visit/widgets/parts/quantity_control.dart';

/// Karta wybranej części z kontrolkami ilości i opcją usunięcia
class SelectedPartCard extends StatelessWidget {
  final dynamic part; // ServicemanPart
  final double quantity;

  const SelectedPartCard({
    super.key,
    required this.part,
    required this.quantity,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Informacje o części z przyciskiem usunięcia
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: PartInfoDisplay(
                    itemCode: part.itemCode,
                    itemDescription: part.itemDescription,
                    warehouseQuantity: part.warehouseQuantity,
                    unit: part.unit,
                    showWarehouse: false, // Pokażemy to osobno
                  ),
                ),
                IconButton(
                  icon: Icon(
                    Icons.remove_circle,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  onPressed: () => context.read<VisitPartsCubit>().removePart(part),
                  tooltip: 'Usuń część',
                ),
              ],
            ),

            // Separator
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 12),
              child: Divider(height: 1),
            ),

            // Sekcja ilości i dostępności
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Informacja o dostępności w magazynie
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'W magazynie: ',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      Text(
                        '${part.warehouseQuantity} ${part.unit}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                    ],
                  ),
                ),

                // Kontrolka ilości
                QuantityControl(
                  itemGID: part.itemGID,
                  quantity: quantity,
                  unit: part.unit,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:serwis_app/visit/cubit/visit_parts_cubit.dart';

/// Komponent wyświetlający wyniki wyszukiwania części
class PartsSearchResults extends StatelessWidget {
  const PartsSearchResults({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<VisitPartsCubit, VisitPartsState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Wyniki wyszukiwania:',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            if (state.status == VisitPartsStatus.loadingAvailableParts)
              const Expanded(
                child: Center(
                  child: CircularProgressIndicator(),
                ),
              )
            else if (state.filteredParts.isNotEmpty)
              Expanded(
                child: Card(
                  margin: EdgeInsets.zero,
                  child: Scrollbar(
                    thumbVisibility: true,
                    child: ListView.separated(
                      shrinkWrap: true,
                      itemCount: state.filteredParts.length,
                      separatorBuilder: (context, index) => const Divider(height: 1),
                      itemBuilder: (context, index) {
                        final part = state.filteredParts[index];
                        return _SearchResultItem(part: part);
                      },
                    ),
                  ),
                ),
              )
            else
              const Expanded(
                child: Center(
                  child: Text('Brak wyników wyszukiwania'),
                ),
              ),
          ],
        );
      },
    );
  }
}

/// Element listy wyników wyszukiwania
class _SearchResultItem extends StatelessWidget {
  final dynamic part; // ServicemanPart

  const _SearchResultItem({required this.part});

  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: Text(part.itemCode),
      subtitle: Text(
        part.itemDescription,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
        style: Theme.of(context).textTheme.labelSmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
      ),
      trailing: Text(
        '${part.warehouseQuantity} ${part.unit}',
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
      ),
      onTap: () => context.read<VisitPartsCubit>().selectPart(part),
    );
  }
}

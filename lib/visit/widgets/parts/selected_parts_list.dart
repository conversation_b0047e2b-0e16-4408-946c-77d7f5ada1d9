import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:serwis_app/core/widgets/loading_widget.dart';
import 'package:serwis_app/visit/cubit/visit_parts_cubit.dart';
import 'package:serwis_app/visit/widgets/parts/selected_part_card.dart';

/// Komponent wyświetlający listę wybranych części z przyciskiem zatwierdzenia
class SelectedPartsList extends StatelessWidget {
  const SelectedPartsList({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<VisitPartsCubit, VisitPartsState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Nagłówek z liczbą wybranych części
            Text(
              'Wybrane części (${state.selectedParts.length})',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),

            // Lista wybranych części
            Expanded(
              child: Scrollbar(
                thumbVisibility: true,
                child: ListView.builder(
                  itemCount: state.selectedParts.length,
                  itemBuilder: (context, index) {
                    // Wyświetlaj części w odwrotnej kolejności (najnowsze na górze)
                    final part = state.selectedParts.reversed.toList()[index];
                    final quantity = state.selectedPartsQuantities[part.itemGID] ?? 1.0;

                    return SelectedPartCard(
                      part: part,
                      quantity: quantity,
                    );
                  },
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Przycisk zatwierdzenia
            LoadingWidget(
              isLoading: state.status == VisitPartsStatus.submittingParts,
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    context.read<VisitPartsCubit>().submitParts();
                  },
                  child: const Text('Zatwierdź wybrane części'),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

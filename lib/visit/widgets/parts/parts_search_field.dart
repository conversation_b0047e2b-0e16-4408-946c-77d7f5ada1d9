import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:serwis_app/visit/cubit/visit_parts_cubit.dart';

/// Komponent pola wyszukiwania części z logiką debounce
class PartsSearchField extends StatefulWidget {
  const PartsSearchField({super.key});

  @override
  State<PartsSearchField> createState() => _PartsSearchFieldState();
}

class _PartsSearchFieldState extends State<PartsSearchField> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
    _searchFocusNode.requestFocus();
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    // Anuluj poprzedni timer
    _debounceTimer?.cancel();

    // Ustaw nowy timer z opóźnieniem
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      final query = _searchController.text;
      context.read<VisitPartsCubit>().searchParts(query);
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<VisitPartsCubit, VisitPartsState>(
      listener: (context, state) {
        // Wyczyść pole wyszukiwania gdy search query zostanie wyczyszczone w Cubit
        if (state.searchQuery.isEmpty && _searchController.text.isNotEmpty) {
          _searchController.clear();
        }
      },
      child: BlocBuilder<VisitPartsCubit, VisitPartsState>(
        builder: (context, state) {
          return TextField(
            controller: _searchController,
            focusNode: _searchFocusNode,
            decoration: InputDecoration(
              labelText: 'Wyszukaj część',
              hintText: 'Wprowadź kod lub opis części',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: state.searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        context.read<VisitPartsCubit>().clearSearch();
                      },
                    )
                  : null,
            ),
          );
        },
      ),
    );
  }
}

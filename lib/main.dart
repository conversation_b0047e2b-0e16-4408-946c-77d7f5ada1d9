import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';

import 'package:serwis_app/core/app_theme.dart';
import 'package:serwis_app/core/auth/auth_repository.dart';
import 'package:serwis_app/core/auth/cubit/auth_cubit.dart';
import 'package:serwis_app/core/config.dart';
import 'package:serwis_app/core/services/photo_service.dart';
import 'package:serwis_app/home/<USER>/home_cubit.dart';
import 'package:serwis_app/home/<USER>/home_screen.dart';
import 'package:serwis_app/login/cubit/login_cubit.dart';
import 'package:serwis_app/login/login_screen.dart';
import 'package:serwis_app/visit/repository/serviceman_parts_repository.dart';
import 'package:serwis_app/visit/repository/task_repository.dart';
import 'package:serwis_app/visit/repository/visit_parts_repository.dart';
import 'package:serwis_app/visit/repository/visit_photos_repository.dart';
import 'package:serwis_app/visit/repository/visit_repository.dart';
import 'package:serwis_app/visit/services/visit_photos_cache_service.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  HydratedBloc.storage = await HydratedStorage.build(
    storageDirectory: kIsWeb
        ? HydratedStorageDirectory.web
        : HydratedStorageDirectory((await getApplicationDocumentsDirectory()).path),
  );

  await Config.init();

  runApp(const MainApp());
}

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    return RepositoryProvider(
      create: (_) => AuthRepository(),
      child: MultiProvider(
        providers: [
          Provider<PhotoService>(
            create: (_) => PhotoService.instance,
          ),
          Provider<VisitPhotosCacheService>(
            create: (_) => VisitPhotosCacheService.instance,
          ),
          BlocProvider(
            create: (context) => AuthCubit(
              authRepository: context.read<AuthRepository>(),
            ),
          ),
          BlocProvider(
            create: (context) => LoginCubit(
              authRepository: context.read<AuthRepository>(),
              authCubit: context.read<AuthCubit>(),
            ),
          ),
          Provider<VisitRepository>(
            create: (_) => VisitRepository(),
          ),
          ProxyProvider<VisitPhotosCacheService, VisitPhotosRepository>(
            update: (context, cacheService, visitPhotosRepository) => VisitPhotosRepository(
              cacheService: cacheService,
            ),
          ),
          Provider<VisitPartsRepository>(
            create: (_) => VisitPartsRepository(),
          ),
          Provider<ServicemanPartsRepository>(
            create: (_) => ServicemanPartsRepository(),
          ),
          Provider<TaskRepository>(
            create: (_) => TaskRepository(),
          ),
        ],
        child: MaterialApp(
          debugShowCheckedModeBanner: false,
          themeMode: ThemeMode.dark,
          darkTheme: AppTheme.theme,
          home: BlocBuilder<AuthCubit, AuthState>(
            builder: (context, state) {
              return state.isAuthenticated
                  ? BlocProvider(
                      create: (context) => HomeCubit(
                        context.read<VisitRepository>(),
                      )..loadData(),
                      child: const HomeScreen(),
                    )
                  : const LoginScreen();
            },
          ),
        ),
      ),
    );
  }
}

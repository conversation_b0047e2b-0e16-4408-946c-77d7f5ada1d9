import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

import 'package:serwis_app/core/auth/model/auth_data.dart';
import 'package:serwis_app/core/config.dart';
import 'package:serwis_app/core/logger.dart';
import 'package:serwis_app/core/services/secure_storage.dart';

// Token Provider dla reaktywnego dostarczania aktualnego tokena
class TokenProvider {
  static final TokenProvider _instance = TokenProvider._internal();
  TokenProvider._internal();
  static TokenProvider get instance => _instance;

  final _storage = SecureStorage.instance;
  String? _cachedToken;

  Future<String?> getCurrentToken() async {
    final authDataRaw = await _storage.read('auth_data');
    if (authDataRaw == null) return null;

    final authData = AuthData.fromJson(authDataRaw);
    _cachedToken = authData.access_token;
    return _cachedToken;
  }

  void updateCachedToken(String newToken) {
    _cachedToken = newToken;
  }

  String? get cachedToken => _cachedToken;
}

final _defaultDio = Dio()
  ..options.baseUrl = Config.read(ConfigKeys.apiUrl)
  ..options.headers['Content-Type'] = 'application/json'
  ..options.headers['Accept'] = 'application/json'
  ..options.receiveTimeout = const Duration(seconds: 30)
  ..options.connectTimeout = const Duration(seconds: 30)
  ..interceptors.add(
    LogInterceptor(
      request: false,
      requestHeader: false,
      requestBody: false,
      responseHeader: false,
    ),
  );

abstract class UnauthenticatedRepository {
  UnauthenticatedRepository() {
    (api.httpClientAdapter as IOHttpClientAdapter).createHttpClient =
        () => HttpClient()..badCertificateCallback = (X509Certificate? cert, String host, int? port) => true;
  }
  final api = _defaultDio;
}

abstract class AuthenticatedRepository extends UnauthenticatedRepository {
  final TokenProvider _tokenProvider = TokenProvider.instance;

  AuthenticatedRepository();

  @override
  Dio get api {
    final dio = super.api;

    // Dodaj interceptor do dynamicznego ustawiania tokena
    if (!dio.interceptors.any((i) => i is AuthTokenInterceptor)) {
      dio.interceptors.add(AuthTokenInterceptor(_tokenProvider));
    }

    if (!dio.interceptors.any((i) => i is RefreshTokenInterceptor)) {
      dio.interceptors.add(RefreshTokenInterceptor());
    }

    return dio;
  }
}

// Interceptor do dynamicznego ustawiania tokena w każdym żądaniu
class AuthTokenInterceptor extends Interceptor {
  final TokenProvider _tokenProvider;

  AuthTokenInterceptor(this._tokenProvider);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    final token = await _tokenProvider.getCurrentToken();
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    handler.next(options);
  }
}

class RefreshTokenInterceptor extends Interceptor {
  final _storage = SecureStorage.instance;
  final _tokenProvider = TokenProvider.instance;
  int _retryCount = 0;
  static const int _maxRetries = 3;

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    final response = err.response;
    if (response?.statusCode == 401 && _retryCount < _maxRetries) {
      _retryCount++;
      try {
        final authDataRaw = await _storage.read('auth_data');
        if (authDataRaw == null) {
          Logger.log('No auth data found');
          return handler.reject(err);
        }
        var authData = AuthData.fromJson(authDataRaw);

        final body = FormData.fromMap({
          "grant_type": 'refresh_token',
          "client_id": dotenv.env['CLIENT_ID'],
          "client_secret": dotenv.env['CLIENT_SECRET'],
          "refresh_token": authData.refresh_token,
        });

        final refreshTokenResponse = await _defaultDio.post('/token', data: body);
        authData = AuthData.fromMap(refreshTokenResponse.data);
        await _storage.write('auth_data', authData.toJson());

        // KLUCZOWE: Zaktualizuj token w TokenProvider
        _tokenProvider.updateCachedToken(authData.access_token);

        final headers = Map<String, dynamic>.from(err.requestOptions.headers);
        headers['Authorization'] = 'Bearer ${authData.access_token}';

        final options = Options(
          method: err.requestOptions.method,
          headers: headers,
        );
        final response = await _defaultDio.request(
          err.requestOptions.path,
          data: err.requestOptions.data,
          queryParameters: err.requestOptions.queryParameters,
          options: options,
        );

        handler.resolve(response);
      } on DioException catch (e) {
        Logger.log(e);
        handler.reject(e);
      } finally {
        _retryCount = 0;
      }
    } else {
      handler.next(err);
    }
  }
}

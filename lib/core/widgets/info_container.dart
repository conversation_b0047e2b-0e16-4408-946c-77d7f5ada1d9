import 'package:flutter/material.dart';

/// Widget do wyświetlania informacji pomocniczych
///
/// Wyświetla subtelną informację pomocniczą w kontenerze z delikatnym tłem.
/// Je<PERSON><PERSON> [visible] jest ustawione na false lub [message] jest null/empty, widget nie zajmuje miejsca.
class InfoContainer extends StatelessWidget {
  /// Treść informacji do wyświetlenia
  final String message;

  /// <PERSON><PERSON> kontener ma być widoczny
  final bool visible;

  /// Ikona do wyświetlenia obok tekstu
  final IconData? icon;

  /// Dodatkowy padding wewnętrzny
  final EdgeInsetsGeometry padding;

  /// Marginesy zewnętrzne
  final EdgeInsetsGeometry margin;

  /// Promień zaokrąglenia rogów
  final double borderRadius;

  /// Rozmiar ikony
  final double iconSize;

  /// Rozmiar czcionki
  final double fontSize;

  const InfoContainer({
    super.key,
    required this.message,
    this.visible = true,
    this.icon = Icons.info_outline,
    this.padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
    this.margin = const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
    this.borderRadius = 8.0,
    this.iconSize = 16.0,
    this.fontSize = 12.0,
  });

  @override
  Widget build(BuildContext context) {
    // Jeśli nie ma wiadomości lub widget nie ma być widoczny, zwracamy pusty kontener
    if (message.isEmpty || !visible) {
      return const SizedBox.shrink();
    }

    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      width: double.infinity,
      margin: margin,
      padding: padding,
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(borderRadius),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              size: iconSize,
              color: colorScheme.onSurfaceVariant,
            ),
            const SizedBox(width: 8),
          ],
          Expanded(
            child: Text(
              message,
              style: textTheme.bodySmall?.copyWith(
                color: colorScheme.onSurfaceVariant,
                fontSize: fontSize,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

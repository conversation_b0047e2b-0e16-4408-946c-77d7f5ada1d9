import 'package:flutter/material.dart';

import 'package:dio/dio.dart';

class ErrorDialog extends StatelessWidget {
  final dynamic error;
  final String title;
  final String message;
  final String buttonText;

  const ErrorDialog({
    super.key,
    required this.error,
    this.title = 'Wystąpił błąd',
    this.message = 'Prosimy spróbować ponownie',
    this.buttonText = 'OK',
  });

  @override
  Widget build(BuildContext context) {
    final errorMessage = _getErrorMessage(error);

    return AlertDialog(
      title: Text(title),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(message),
          if (errorMessage != null) ...[
            const SizedBox(height: 8),
            Text(
              errorMessage,
              style: Theme.of(context).textTheme.labelSmall,
            ),
          ],
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(buttonText),
        ),
      ],
    );
  }

  String? _getErrorMessage(dynamic error) {
    if (error == null) {
      return null;
    }

    if (error is DioException) {
      final dioError = error;
      return dioError.message;
    }

    return error.toString();
  }

  // Metoda pomocnicza do wyświetlania dialogu
  static Future<void> show(
    BuildContext context,
    dynamic error, {
    String title = 'Wystąpił błąd',
    String message = 'Prosimy spróbować ponownie',
    String buttonText = 'OK',
  }) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return ErrorDialog(
          error: error,
          title: title,
          message: message,
          buttonText: buttonText,
        );
      },
    );
  }
}

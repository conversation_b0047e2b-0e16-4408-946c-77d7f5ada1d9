import 'package:flutter/material.dart';

class LoadingWidget extends StatelessWidget {
  final bool isLoading;
  final Widget child;

  const LoadingWidget({
    super.key,
    this.isLoading = true,
    this.child = const SizedBox.shrink(),
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Opacity(
          opacity: isLoading ? 0 : 1,
          child: child,
        ),
        if (isLoading)
          const SizedBox(
            height: 26,
            width: 26,
            child: CircularProgressIndicator(),
          ),
      ],
    );
  }
}

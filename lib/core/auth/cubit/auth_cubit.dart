import 'package:equatable/equatable.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';

import 'package:serwis_app/core/auth/auth_repository.dart';
import 'package:serwis_app/core/auth/model/auth_credentials.dart';
import 'package:serwis_app/core/auth/model/auth_data.dart';
import 'package:serwis_app/core/repository.dart';

class AuthState extends Equatable {
  final AuthData? authData;
  final String? login;

  const AuthState({
    this.authData,
    this.login,
  });

  bool get isAuthenticated => authData != null;

  Map<String, dynamic> toMap() {
    return {
      'authData': authData?.toMap(),
      'login': login,
    };
  }

  factory AuthState.fromMap(Map<String, dynamic> map) {
    return AuthState(
      authData: map['authData'] != null ? AuthData.fromMap(map['authData']) : null,
      login: map['login'],
    );
  }

  @override
  List<Object?> get props => [authData, login];
}

class AuthCubit extends HydratedCubit<AuthState> {
  AuthCubit({
    required this.authRepository,
  }) : super(const AuthState()) {
    init();
  }

  final AuthRepository authRepository;
  final _tokenProvider = TokenProvider.instance;

  Future<void> init() async {
    try {
      final authData = await authRepository.getAuthData();
      final credentials = await authRepository.getCredentials();

      // Zaktualizuj token w TokenProvider
      if (authData?.access_token != null) {
        _tokenProvider.updateCachedToken(authData!.access_token);
      }

      emit(AuthState(authData: authData, login: credentials?.login));
    } catch (e) {
      logOut();
    }
  }

  Future<void> logIn(AuthCredentials credentials, bool rememberCredentials) async {
    final data = await authRepository.login(credentials, rememberCredentials);

    // Zaktualizuj token w TokenProvider
    _tokenProvider.updateCachedToken(data.access_token);

    emit(AuthState(authData: data, login: credentials.login));
  }

  void logOut() {
    authRepository.logout();
    emit(const AuthState());
  }

  @override
  AuthState? fromJson(Map<String, dynamic> json) {
    try {
      return AuthState.fromMap(json);
    } catch (_) {
      return null;
    }
  }

  @override
  Map<String, dynamic>? toJson(AuthState state) {
    try {
      return state.toMap();
    } catch (_) {
      return null;
    }
  }
}
